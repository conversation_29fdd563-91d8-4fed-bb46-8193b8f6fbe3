

services:
  comfyui:
    image: cafelabs-zf-comfyui:0.37
    container_name: cafelabs-zf-comfyui-0.37
    command: bash -c "source /etc/profile && source /root/miniconda3/etc/profile.d/conda.sh && conda activate comfyui && cd ComfyUI/ && python main.py --listen 0.0.0.0 --port 8888"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    ports:
      - "8888:8888"
    volumes:
      - "L:/models:/ComfyUI/models"